{"name": "hono-api-kerja-praktik", "scripts": {"dev": "bun run --hot src/index.ts"}, "prisma": {"seed": "bun run --hot prisma/seed.ts"}, "dependencies": {"@hono/zod-validator": "^0.4.3", "@prisma/client": "6.6.0", "colorette": "^2.0.20", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "hono": "^4.7.7", "prisma": "^6.6.0", "simple-crypto-js": "^3.0.1", "zod": "^3.24.3"}, "devDependencies": {"@types/bun": "latest"}}